# Linux.do OAuth2 快捷登录功能

## 功能概述

本项目已成功集成Linux.do OAuth2快捷登录功能，用户可以使用Linux.do账户快速登录，未注册用户将自动注册后登录。

## 实现的功能

### 1. OAuth2配置 (oauth.py)
- 配置了Linux.do OAuth2的客户端信息
- Client ID: `GZfEhWWjowkChfAn5xtD6SFNfxR9YUt9`
- Client Secret: `e5FiHlz6YwBdEJOw1VtbOYpAt7TTr4J8`
- 回调地址: `https://sd.exacg.cc/linux`
- 授权端点: `https://connect.linux.do/oauth2/authorize`
- 令牌端点: `https://connect.linux.do/oauth2/token`
- 用户信息端点: `https://connect.linux.do/api/user`

### 2. 用户管理扩展 (auth.py)
- 扩展了UserManager类以支持OAuth2用户
- 新增`register_oauth_user()`方法用于OAuth2用户注册
- 新增`login_oauth_user()`方法用于OAuth2用户登录
- 新增`get_user_by_oauth()`方法用于通过OAuth信息查找用户
- OAuth2用户数据结构包含：
  - `oauth_provider`: OAuth提供商（'linux_do'）
  - `oauth_id`: Linux.do用户ID
  - `oauth_username`: Linux.do用户名
  - `avatar_url`: 用户头像URL
  - `original_user_info`: 原始用户信息

### 3. 路由处理 (app.py)
- `/oauth/linux_do`: 启动OAuth2授权流程
- `/linux`: OAuth2回调处理端点
- 支持状态验证防止CSRF攻击
- 自动处理用户注册和登录流程
- 错误处理和用户友好的错误消息

### 4. 前端界面 (templates/login.html)
- 在登录页面添加了"使用 Linux.do 快捷登录"按钮
- 美观的OAuth按钮样式
- 支持显示OAuth登录过程中的状态消息
- 自动处理URL参数中的错误和成功消息

## 使用流程

### 用户登录流程
1. 用户访问登录页面 `/login`
2. 点击"使用 Linux.do 快捷登录"按钮
3. 重定向到Linux.do授权页面
4. 用户在Linux.do上授权应用访问
5. 重定向回应用的`/linux`回调端点
6. 应用获取用户信息并处理登录/注册

### 新用户自动注册
- 如果用户首次使用Linux.do登录，系统会自动创建账户
- 用户名格式：`linux_do_{linux_do_username}`
- 根据系统设置决定是否需要管理员审核
- 注册成功后自动登录（如不需审核）

### 已有用户登录
- 系统通过OAuth ID识别已注册用户
- 更新最后登录时间和用户信息
- 直接登录到系统

## 安全特性

1. **状态验证**: 使用随机生成的state参数防止CSRF攻击
2. **令牌安全**: 访问令牌仅用于获取用户信息，不存储
3. **用户隔离**: OAuth用户与普通用户通过用户名前缀区分
4. **错误处理**: 完善的错误处理和用户友好的错误消息

## 配置说明

### 环境要求
- Python 3.7+
- Flask
- requests-oauthlib

### 依赖包
```bash
pip install requests-oauthlib
```

### 系统设置
- 可在管理员面板中设置是否需要审核新注册用户
- OAuth2用户同样受审核设置影响

## 测试验证

已通过以下测试验证功能正常：
1. ✅ OAuth2授权URL生成正确
2. ✅ 重定向到Linux.do授权页面成功
3. ✅ 回调端点正确处理授权码
4. ✅ 用户信息获取和处理正常
5. ✅ 前端界面显示正常
6. ✅ 错误处理和消息显示正常

## 注意事项

1. **回调地址**: 确保Linux.do应用配置中的回调地址与代码中的`redirect_uri`一致
2. **HTTPS要求**: 生产环境中建议使用HTTPS
3. **用户名冲突**: OAuth用户使用特殊前缀避免与普通用户冲突
4. **数据同步**: OAuth用户信息会在每次登录时更新

## 故障排除

### 常见错误
- `oauth_init_failed`: OAuth授权初始化失败，检查网络连接
- `state_mismatch`: 状态参数不匹配，可能是CSRF攻击或会话过期
- `token_failed`: 获取令牌失败，检查客户端配置
- `user_info_failed`: 获取用户信息失败，检查API端点

### 调试建议
- 检查应用日志获取详细错误信息
- 验证Linux.do应用配置
- 确认网络连接正常
- 检查回调地址配置
