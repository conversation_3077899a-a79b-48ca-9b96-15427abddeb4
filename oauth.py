import os
import secrets
import requests
from urllib.parse import urlencode, parse_qs, urlparse
from requests_oauthlib import OAuth2Session
import logging

logger = logging.getLogger(__name__)

class LinuxDoOAuth:
    """Linux.do OAuth2 认证处理类"""
    
    def __init__(self):
        # Linux.do OAuth2 配置
        self.client_id = "GZfEhWWjowkChfAn5xtD6SFNfxR9YUt9"
        self.client_secret = "e5FiHlz6YwBdEJOw1VtbOYpAt7TTr4J8"
        self.redirect_uri = "https://sd.exacg.cc/linux"
        
        # Linux.do OAuth2 端点 (Discourse OAuth2)
        self.authorization_base_url = "https://connect.linux.do/oauth2/authorize"
        self.token_url = "https://connect.linux.do/oauth2/token"
        self.user_info_url = "https://connect.linux.do/api/user"
        
        # OAuth2 作用域
        self.scope = ["read"]
    
    def get_authorization_url(self, state=None):
        """
        获取授权URL
        
        Args:
            state: 状态参数，用于防止CSRF攻击
            
        Returns:
            tuple: (authorization_url, state)
        """
        if not state:
            state = secrets.token_urlsafe(32)
        
        oauth = OAuth2Session(
            client_id=self.client_id,
            redirect_uri=self.redirect_uri,
            scope=self.scope,
            state=state
        )
        
        authorization_url, state = oauth.authorization_url(
            self.authorization_base_url,
            state=state
        )
        
        logger.info(f"生成授权URL: {authorization_url}")
        return authorization_url, state
    
    def get_access_token(self, authorization_response_url, state):
        """
        通过授权码获取访问令牌

        Args:
            authorization_response_url: 回调URL（包含授权码）
            state: 状态参数

        Returns:
            dict: 包含访问令牌的字典，失败时返回None
        """
        try:
            logger.info(f"开始获取访问令牌，回调URL: {authorization_response_url}")
            logger.info(f"使用的token_url: {self.token_url}")
            logger.info(f"使用的redirect_uri: {self.redirect_uri}")
            logger.info(f"使用的client_id: {self.client_id}")

            oauth = OAuth2Session(
                client_id=self.client_id,
                redirect_uri=self.redirect_uri,
                state=state
            )

            # 尝试使用标准的OAuth2流程
            token = oauth.fetch_token(
                self.token_url,
                authorization_response=authorization_response_url,
                client_secret=self.client_secret,
                timeout=30
            )

            logger.info("成功获取访问令牌")
            logger.info(f"令牌类型: {token.get('token_type', 'unknown')}")
            return token

        except Exception as e:
            logger.error(f"获取访问令牌失败: {e}")
            logger.error(f"异常类型: {type(e).__name__}")

            # 尝试手动构建请求
            try:
                logger.info("尝试手动获取访问令牌...")
                return self._manual_token_request(authorization_response_url)
            except Exception as manual_e:
                logger.error(f"手动获取令牌也失败: {manual_e}")
                return None

    def _manual_token_request(self, authorization_response_url):
        """
        手动构建令牌请求（备用方法）
        """
        from urllib.parse import parse_qs, urlparse

        # 从回调URL中提取授权码
        parsed_url = urlparse(authorization_response_url)
        query_params = parse_qs(parsed_url.query)

        if 'code' not in query_params:
            raise ValueError("回调URL中没有找到授权码")

        auth_code = query_params['code'][0]
        logger.info(f"提取到授权码: {auth_code[:10]}...")

        # 构建令牌请求
        token_data = {
            'grant_type': 'authorization_code',
            'code': auth_code,
            'redirect_uri': self.redirect_uri,
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }

        logger.info(f"发送令牌请求到: {self.token_url}")
        response = requests.post(
            self.token_url,
            data=token_data,
            headers=headers,
            timeout=30
        )

        logger.info(f"令牌请求响应状态: {response.status_code}")
        logger.info(f"令牌请求响应内容: {response.text}")

        if response.status_code == 200:
            token_info = response.json()
            logger.info("手动获取访问令牌成功")
            return token_info
        else:
            raise Exception(f"令牌请求失败: {response.status_code} - {response.text}")
    
    def get_user_info(self, access_token):
        """
        使用访问令牌获取用户信息

        Args:
            access_token: 访问令牌

        Returns:
            dict: 用户信息，失败时返回None
        """
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            logger.info(f"获取用户信息，使用端点: {self.user_info_url}")
            response = requests.get(self.user_info_url, headers=headers, timeout=10)

            logger.info(f"用户信息请求响应状态: {response.status_code}")
            logger.info(f"用户信息请求响应内容: {response.text[:500]}...")

            response.raise_for_status()

            user_info = response.json()
            logger.info(f"成功获取用户信息: {user_info.get('username', 'unknown')}")

            return user_info

        except requests.exceptions.RequestException as e:
            logger.error(f"获取用户信息失败: {e}")

            # 尝试其他可能的端点
            try:
                logger.info("尝试使用备用端点获取用户信息...")
                alt_url = "https://connect.linux.do/session/current.json"
                response = requests.get(alt_url, headers=headers, timeout=10)
                logger.info(f"备用端点响应状态: {response.status_code}")
                logger.info(f"备用端点响应内容: {response.text[:500]}...")

                if response.status_code == 200:
                    user_info = response.json()
                    logger.info("使用备用端点成功获取用户信息")
                    return user_info

            except Exception as alt_e:
                logger.error(f"备用端点也失败: {alt_e}")

            return None
        except Exception as e:
            logger.error(f"解析用户信息失败: {e}")
            return None
    
    def create_user_from_oauth(self, user_info):
        """
        从OAuth用户信息创建本地用户数据
        
        Args:
            user_info: Linux.do返回的用户信息
            
        Returns:
            dict: 本地用户数据格式
        """
        # 从Linux.do用户信息提取必要字段
        username = user_info.get('username', '')
        email = user_info.get('email', '')
        user_id = user_info.get('id', '')
        avatar_url = user_info.get('avatar_template', '')
        
        # 为OAuth用户生成特殊的用户名前缀，避免与普通用户冲突
        local_username = f"linux_do_{username}" if username else f"linux_do_user_{user_id}"
        
        # 创建本地用户数据结构
        local_user_data = {
            'username': local_username,
            'email': email,
            'oauth_provider': 'linux_do',
            'oauth_id': str(user_id),
            'oauth_username': username,
            'avatar_url': avatar_url,
            'original_user_info': user_info  # 保存原始用户信息以备后用
        }
        
        logger.info(f"创建OAuth用户数据: {local_username}")
        return local_user_data
    
    def validate_callback_request(self, request_args):
        """
        验证OAuth回调请求
        
        Args:
            request_args: Flask request.args
            
        Returns:
            tuple: (is_valid, error_message, code, state)
        """
        # 检查是否有错误参数
        if 'error' in request_args:
            error = request_args.get('error', 'unknown_error')
            error_description = request_args.get('error_description', '未知错误')
            logger.error(f"OAuth回调错误: {error} - {error_description}")
            return False, f"授权失败: {error_description}", None, None
        
        # 检查必要参数
        code = request_args.get('code')
        state = request_args.get('state')
        
        if not code:
            logger.error("OAuth回调缺少授权码")
            return False, "授权码缺失", None, None
        
        if not state:
            logger.error("OAuth回调缺少状态参数")
            return False, "状态参数缺失", None, None
        
        logger.info("OAuth回调请求验证通过")
        return True, None, code, state


def get_linux_do_oauth():
    """获取Linux.do OAuth实例的工厂函数"""
    return LinuxDoOAuth()
