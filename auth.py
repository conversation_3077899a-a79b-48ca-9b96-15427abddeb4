import hashlib
import secrets
import json
import os
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect, url_for

class UserManager:
    def __init__(self, data_file='users.json'):
        self.data_file = data_file
        self.users = self.load_users()
    
    def load_users(self):
        """加载用户数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def save_users(self):
        """保存用户数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
    
    def hash_password(self, password):
        """密码加密"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return salt + password_hash.hex()
    
    def verify_password(self, password, hashed_password):
        """验证密码"""
        salt = hashed_password[:32]
        stored_hash = hashed_password[32:]
        password_hash = hashlib.pbkdf2_hmac('sha256',
                                          password.encode('utf-8'),
                                          salt.encode('utf-8'),
                                          100000)
        return password_hash.hex() == stored_hash
    
    def register_user(self, username, password, email=None, require_approval=False):
        """用户注册"""
        if username in self.users:
            return False, "用户名已存在"

        if len(username) < 3:
            return False, "用户名至少需要3个字符"

        if len(password) < 6:
            return False, "密码至少需要6个字符"

        # 根据系统设置决定用户状态
        if require_approval:
            user_status = 'pending'  # 待审核
            success_message = "注册成功，请等待管理员审核"
            initial_points = 0  # 待审核用户暂不赠送积分
        else:
            user_status = 'approved'  # 直接通过
            success_message = "注册成功，赠送10点积分"
            initial_points = 10  # 新用户赠送10点积分

        # 创建新用户
        user_data = {
            'username': username,
            'password': self.hash_password(password),
            'email': email or '',
            'points': initial_points,
            'created_at': datetime.now().isoformat(),
            'last_login': None,
            'is_admin': False,
            'total_generated': 0,  # 总生成次数
            'generation_history': [],  # 生成历史
            'status': user_status,  # 用户状态：pending, approved, rejected
            'approved_at': None if require_approval else datetime.now().isoformat(),  # 审核通过时间
            'approved_by': None  # 审核人
        }

        self.users[username] = user_data

        if self.save_users():
            return True, success_message
        else:
            return False, "注册失败，请重试"
    
    def login_user(self, username, password):
        """用户登录"""
        if username not in self.users:
            return False, "用户名不存在"

        user = self.users[username]
        if not self.verify_password(password, user['password']):
            return False, "密码错误"

        # 检查用户审核状态
        user_status = user.get('status', 'approved')  # 兼容旧数据，默认为已通过
        if user_status == 'pending':
            return False, "您的账户正在等待管理员审核，请耐心等待"
        elif user_status == 'rejected':
            return False, "您的账户审核未通过，请联系管理员"

        # 更新最后登录时间
        user['last_login'] = datetime.now().isoformat()
        self.save_users()

        return True, "登录成功"
    
    def get_user(self, username):
        """获取用户信息"""
        return self.users.get(username)
    
    def update_user_points(self, username, points_change):
        """更新用户积分"""
        if username not in self.users:
            return False, "用户不存在"
        
        user = self.users[username]
        new_points = user['points'] + points_change
        
        if new_points < 0:
            return False, "积分不足"
        
        user['points'] = new_points
        self.save_users()
        return True, f"积分更新成功，当前积分：{new_points}"
    
    def add_generation_record(self, username, generation_type, prompt, success=True):
        """添加生成记录"""
        if username not in self.users:
            return False
        
        user = self.users[username]
        record = {
            'type': generation_type,  # 'image' or 'video'
            'prompt': prompt[:100],  # 只保存前100个字符
            'timestamp': datetime.now().isoformat(),
            'success': success
        }
        
        user['generation_history'].append(record)
        
        # 只保留最近100条记录
        if len(user['generation_history']) > 100:
            user['generation_history'] = user['generation_history'][-100:]
        
        if success:
            user['total_generated'] += 1
        
        self.save_users()
        return True
    
    def get_user_stats(self, username):
        """获取用户统计信息"""
        if username not in self.users:
            return None

        user = self.users[username]
        recent_history = user['generation_history'][-10:]  # 最近10条记录

        return {
            'username': username,
            'points': user['points'],
            'total_generated': user['total_generated'],
            'created_at': user['created_at'],
            'last_login': user['last_login'],
            'recent_history': recent_history,
            'status': user.get('status', 'approved'),  # 兼容旧数据
            'is_admin': user.get('is_admin', False)
        }

    def approve_user(self, username, admin_username):
        """管理员审核通过用户"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if user.get('status') != 'pending':
            return False, "用户不在待审核状态"

        # 更新用户状态
        user['status'] = 'approved'
        user['approved_at'] = datetime.now().isoformat()
        user['approved_by'] = admin_username
        user['points'] = 10  # 审核通过后赠送积分

        if self.save_users():
            return True, "用户审核通过，已赠送10点积分"
        else:
            return False, "审核操作失败"

    def reject_user(self, username, admin_username, reason=""):
        """管理员拒绝用户"""
        if username not in self.users:
            return False, "用户不存在"

        user = self.users[username]
        if user.get('status') != 'pending':
            return False, "用户不在待审核状态"

        # 更新用户状态
        user['status'] = 'rejected'
        user['rejected_at'] = datetime.now().isoformat()
        user['rejected_by'] = admin_username
        user['reject_reason'] = reason

        if self.save_users():
            return True, "用户审核已拒绝"
        else:
            return False, "审核操作失败"

    def get_pending_users(self):
        """获取待审核用户列表"""
        pending_users = []
        for username, user_data in self.users.items():
            if user_data.get('status') == 'pending':
                pending_users.append({
                    'username': username,
                    'email': user_data.get('email', ''),
                    'created_at': user_data.get('created_at', ''),
                    'status': user_data.get('status', 'pending')
                })

        # 按注册时间排序，最新的在前
        pending_users.sort(key=lambda x: x['created_at'], reverse=True)
        return pending_users

    def reset_user_password(self, username, new_password, admin_username):
        """管理员重置用户密码"""
        if username not in self.users:
            return False, "用户不存在"

        if len(new_password) < 6:
            return False, "密码至少需要6个字符"

        # 更新用户密码
        self.users[username]['password'] = self.hash_password(new_password)
        self.users[username]['password_reset_at'] = datetime.now().isoformat()
        self.users[username]['password_reset_by'] = admin_username

        if self.save_users():
            return True, f"用户 {username} 的密码已重置"
        else:
            return False, "密码重置失败"

    def delete_user(self, username, admin_username):
        """管理员删除用户"""
        if username not in self.users:
            return False, "用户不存在"

        # 记录删除操作（可选：保存到日志文件）
        deleted_user_info = {
            'username': username,
            'deleted_at': datetime.now().isoformat(),
            'deleted_by': admin_username,
            'user_data': self.users[username].copy()  # 备份用户数据
        }

        # 删除用户
        del self.users[username]

        if self.save_users():
            # 可以选择将删除记录保存到单独的日志文件
            self._log_user_deletion(deleted_user_info)
            return True, f"用户 {username} 已删除"
        else:
            return False, "删除用户失败"

    def _log_user_deletion(self, deletion_info):
        """记录用户删除日志"""
        try:
            log_file = 'deleted_users.json'
            deleted_users = []

            # 读取现有的删除记录
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        deleted_users = json.load(f)
                except (json.JSONDecodeError, IOError):
                    deleted_users = []

            # 添加新的删除记录
            deleted_users.append(deletion_info)

            # 保存删除记录
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(deleted_users, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # 记录日志失败不影响删除操作
            print(f"记录删除日志失败: {e}")

    def register_oauth_user(self, oauth_user_data, require_approval=False):
        """
        注册OAuth用户

        Args:
            oauth_user_data: OAuth用户数据
            require_approval: 是否需要审核

        Returns:
            tuple: (success, message, username)
        """
        username = oauth_user_data.get('username', '')
        email = oauth_user_data.get('email', '')
        oauth_provider = oauth_user_data.get('oauth_provider', '')
        oauth_id = oauth_user_data.get('oauth_id', '')
        oauth_username = oauth_user_data.get('oauth_username', '')

        if not username:
            return False, "用户名不能为空", None

        # 检查用户名是否已存在
        if username in self.users:
            return False, "用户名已存在", None

        # 检查是否已经有相同的OAuth用户
        for existing_username, user_data in self.users.items():
            if (user_data.get('oauth_provider') == oauth_provider and
                user_data.get('oauth_id') == oauth_id):
                return False, "该OAuth账户已绑定其他用户", existing_username

        # 根据系统设置决定用户状态
        if require_approval:
            user_status = 'pending'
            success_message = "OAuth注册成功，请等待管理员审核"
            initial_points = 0
        else:
            user_status = 'approved'
            success_message = "OAuth注册成功，赠送10点积分"
            initial_points = 10

        # 创建OAuth用户
        user_data = {
            'username': username,
            'password': None,  # OAuth用户没有密码
            'email': email or '',
            'points': initial_points,
            'created_at': datetime.now().isoformat(),
            'last_login': datetime.now().isoformat(),
            'is_admin': False,
            'total_generated': 0,
            'generation_history': [],
            'status': user_status,
            'approved_at': None if require_approval else datetime.now().isoformat(),
            'approved_by': None,
            'oauth_provider': oauth_provider,
            'oauth_id': oauth_id,
            'oauth_username': oauth_username,
            'avatar_url': oauth_user_data.get('avatar_url', ''),
            'original_user_info': oauth_user_data.get('original_user_info', {})
        }

        self.users[username] = user_data

        if self.save_users():
            return True, success_message, username
        else:
            return False, "注册失败，请重试", None

    def login_oauth_user(self, oauth_user_data):
        """
        OAuth用户登录

        Args:
            oauth_user_data: OAuth用户数据

        Returns:
            tuple: (success, message, username)
        """
        oauth_provider = oauth_user_data.get('oauth_provider', '')
        oauth_id = oauth_user_data.get('oauth_id', '')

        # 查找已存在的OAuth用户
        for username, user_data in self.users.items():
            if (user_data.get('oauth_provider') == oauth_provider and
                user_data.get('oauth_id') == oauth_id):

                # 检查用户审核状态
                user_status = user_data.get('status', 'approved')
                if user_status == 'pending':
                    return False, "您的账户正在等待管理员审核，请耐心等待", None
                elif user_status == 'rejected':
                    return False, "您的账户审核未通过，请联系管理员", None

                # 更新最后登录时间和用户信息
                user_data['last_login'] = datetime.now().isoformat()

                # 更新用户信息（如头像等可能会变化）
                if 'avatar_url' in oauth_user_data:
                    user_data['avatar_url'] = oauth_user_data['avatar_url']
                if 'original_user_info' in oauth_user_data:
                    user_data['original_user_info'] = oauth_user_data['original_user_info']

                self.save_users()
                return True, "登录成功", username

        # 用户不存在，需要注册
        return False, "用户不存在，需要注册", None

    def get_user_by_oauth(self, oauth_provider, oauth_id):
        """
        通过OAuth信息查找用户

        Args:
            oauth_provider: OAuth提供商
            oauth_id: OAuth用户ID

        Returns:
            dict: 用户信息，不存在时返回None
        """
        for username, user_data in self.users.items():
            if (user_data.get('oauth_provider') == oauth_provider and
                user_data.get('oauth_id') == oauth_id):
                return user_data
        return None

# 装饰器：要求用户登录
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：要求管理员权限
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        
        # 这里需要在app.py中初始化user_manager后才能使用
        # 暂时简化处理
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：检查积分是否足够
def points_required(points_needed=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'username' not in session:
                if request.is_json:
                    return jsonify({'success': False, 'message': '请先登录'}), 401
                return redirect(url_for('login'))
            
            # 这里需要在具体使用时检查积分
            return f(*args, **kwargs)
        return decorated_function
    return decorator
